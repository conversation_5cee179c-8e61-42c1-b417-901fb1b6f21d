//
//  UserHelper.swift
//  Bj Beauty
//
//  Created by ahmed ezz on 2/18/20.
//  Copyright © 2020 aldar. All rights reserved.
//

import UIKit

class UserHelper {
    
    public static var user = UserHelper()
    
    func getUserId() -> String {
        return  SharedHelper.shared.getFromDefault(key: "userId").isEmpty ? "0" : SharedHelper.shared.getFromDefault(key: "userId")
    }
    
    func getUserType() -> String {
        return  SharedHelper.shared.getFromDefault(key: "userType").isEmpty ? "0" : SharedHelper.shared.getFromDefault(key: "userType")
    }
    
    func saveUserId(id: Int) {
        SharedHelper.shared.saveInDefault(key: "userId",value: id.description)
    }
    
    func getUserToken() -> String {
        return  SharedHelper.shared.getFromDefault(key: "userToken").isEmpty ? "0" : SharedHelper.shared.getFromDef<PERSON>(key: "userToken")
    }
    
    func saveUserToken(token: String) {
        SharedHelper.shared.saveInDefault(key: "userToken",value: token)
    }
    
    func saveUserType(id: Int) {
        SharedHelper.shared.saveInDefault(key: "userType",value: id.description)
    }
    
    func getSearchHistory() -> [String] {
        let searchHistory = SharedHelper.shared.getArray(key: "history")
        return searchHistory
    }
    
    func saveNewSerach(searchName: String) {
        var searchWords = getSearchHistory()
        if !isInSearch(searchName: searchName) {
            searchWords.insert(searchName, at: 0)
            SharedHelper.shared.saveArray(key: "history", value: searchWords )
        }
    }
    
    private func isInSearch(searchName: String) -> Bool {
        let lstSearchWords = getSearchHistory()
        for name in lstSearchWords {
            if name == searchName {
                return true
            }
        }
        return false
    }
    
    func isLogin() -> Bool {
        return getUserToken() != "0" ? true : false
    }
    
    public func logout() {
        saveUserId(id: 0)
        saveUserType(id: 0)
        saveUserToken(token: "")
        saveNotificaions(count: 0)
        SharedHelper.shared.saveInDefault(key: "email", value: "")
        SharedHelper.shared.saveInDefault(key: "mobile", value: "")
        SharedHelper.shared.saveInDefault(key: "fullName", value: "")
        SharedHelper.shared.saveInDefault(key: "history", value: "")
        AppDelegate().refreshNotifications()
        NotificationCenter.default.post(name: NSNotification.Name("AbandonedRequestSent"), object: nil)

        // Reset micro dealer flow properties
        ConstantsValues.sharedInstance.isListingCarInMicroDealerFlowStarted = false
        ConstantsValues.sharedInstance.isRenewingExpiredMicroDealerBundle = false
        ConstantsValues.sharedInstance.renewingExpiredMicroDealerBundleId = 0
        ConstantsValues.sharedInstance.activeSubscriptionMicroDealer = nil
        ConstantsValues.sharedInstance.isOpenBoostVisibilityMicroDealerScreenFromPackages = false
    }
    
    public func getNotificationsCount() -> Int {
        return Int(SharedHelper.shared.getFromDefault(key: "notificationsCount")) ?? 0
    }
    
    public func saveNotificaions(count: Int) {
        AppDelegate().refreshNotifications()
        SharedHelper.shared.saveInDefault(key: "notificationsCount", value: count.description)
    }
    
    public func notificationOpened() {
        let notificationsCount = getNotificationsCount()
        saveNotificaions(count: notificationsCount - 1)
        AppDelegate().refreshNotifications()
    }
    
    public func saveAppleUserId(userId: String, userEmail: String, userName: String) {
        SharedHelper.shared.saveInDefault(key: "appleId", value: userId)
        SharedHelper.shared.saveInDefault(key: "appleName", value: userName)
        SharedHelper.shared.saveInDefault(key: "appleEmail", value: userEmail)
    }
    
    public func getAppleUserData() -> [String:String] {
        let userId = SharedHelper.shared.getFromDefault(key: "appleId")
        let userEmail = SharedHelper.shared.getFromDefault(key: "appleEmail")
        let userName = SharedHelper.shared.getFromDefault(key: "appleName")
        
        return ["SocialAuthID":userId,"Email":userEmail,"FullName":userName]
    }
}
